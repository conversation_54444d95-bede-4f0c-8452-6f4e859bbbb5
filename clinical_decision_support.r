# =============================================================================
# CLINICAL DECISION SUPPORT SYSTEM FOR IBRUTINIB TREATMENT
# =============================================================================
# This script provides comprehensive clinical decision support for Ibrutinib
# treatment based on ML predictions and clinical guidelines
#
# Features:
# - Risk stratification algorithms
# - Dosing recommendations based on patient characteristics
# - Monitoring schedule optimization
# - Treatment discontinuation alerts
# - Clinical guideline integration
# - Real-time decision support
# =============================================================================

# Load required libraries
suppressPackageStartupMessages({
  library(dplyr)
  library(ggplot2)
  library(DT)
  library(shiny)
  library(plotly)
})

# Load ML utilities
if (file.exists("ml_performance_utils.r")) {
  source("ml_performance_utils.r")
}

# =============================================================================
# RISK STRATIFICATION ALGORITHMS
# =============================================================================

#' Advanced Risk Stratification
#' Comprehensive risk assessment using multiple clinical factors
advanced_risk_stratification <- function(patient_data, ml_predictions = NULL) {
  
  cat("Performing advanced risk stratification...\n")
  
  n_patients <- nrow(patient_data)
  
  # Initialize risk assessment framework
  risk_assessment <- data.frame(
    patient_id = patient_data$patient_id,
    age_risk_score = 0,
    comorbidity_risk_score = 0,
    drug_interaction_risk_score = 0,
    genetic_risk_score = 0,
    laboratory_risk_score = 0,
    ml_bleeding_risk = 0,
    ml_major_bleeding_risk = 0,
    composite_risk_score = 0,
    risk_category = "Low",
    confidence_level = "High",
    stringsAsFactors = FALSE
  )
  
  # Age-based risk scoring (evidence-based thresholds)
  if ("age" %in% names(patient_data)) {
    risk_assessment$age_risk_score <- ifelse(patient_data$age >= 75, 3,
                                           ifelse(patient_data$age >= 65, 2, 1))
  }
  
  # Comorbidity risk scoring
  comorbidity_factors <- c("hypertension", "diabetes", "cardiovascular_disease", 
                          "atrial_fibrillation", "high_comorbidity_score")
  available_comorbidities <- intersect(comorbidity_factors, names(patient_data))
  
  if (length(available_comorbidities) > 0) {
    comorbidity_sum <- rowSums(patient_data[available_comorbidities], na.rm = TRUE)
    risk_assessment$comorbidity_risk_score <- pmin(comorbidity_sum, 5)
  }
  
  # Drug interaction risk
  interaction_factors <- c("anticoagulants", "antiplatelets", "cyp3a4_inhibitors")
  available_interactions <- intersect(interaction_factors, names(patient_data))
  
  if (length(available_interactions) > 0) {
    interaction_sum <- rowSums(patient_data[available_interactions], na.rm = TRUE)
    risk_assessment$drug_interaction_risk_score <- pmin(interaction_sum * 2, 6)
  }
  
  # Genetic risk factors
  genetic_factors <- c("plcg2_variants", "btk_c481s_mutation", "cyp3a4_genotype")
  available_genetic <- intersect(genetic_factors, names(patient_data))
  
  if (length(available_genetic) > 0) {
    # CYP3A4 poor metabolizers have higher risk
    if ("metabolizer_phenotype" %in% names(patient_data)) {
      risk_assessment$genetic_risk_score <- ifelse(
        patient_data$metabolizer_phenotype == "Poor", 3,
        ifelse(patient_data$metabolizer_phenotype == "Intermediate", 2, 1)
      )
    }
  }
  
  # Laboratory-based risk
  lab_risk <- 0
  if ("platelet_count" %in% names(patient_data)) {
    lab_risk <- lab_risk + ifelse(patient_data$platelet_count < 100, 2, 0)
  }
  if ("creatinine_clearance" %in% names(patient_data)) {
    lab_risk <- lab_risk + ifelse(patient_data$creatinine_clearance < 60, 2, 0)
  }
  if ("alt" %in% names(patient_data)) {
    lab_risk <- lab_risk + ifelse(patient_data$alt > 100, 1, 0)
  }
  risk_assessment$laboratory_risk_score <- pmin(lab_risk, 5)
  
  # Incorporate ML predictions if available
  if (!is.null(ml_predictions)) {
    if ("bleeding_risk" %in% names(ml_predictions)) {
      risk_assessment$ml_bleeding_risk <- ml_predictions$bleeding_risk * 10
    }
    if ("major_bleeding" %in% names(ml_predictions)) {
      risk_assessment$ml_major_bleeding_risk <- ml_predictions$major_bleeding * 10
    }
  }
  
  # Calculate composite risk score (weighted)
  risk_assessment$composite_risk_score <- (
    0.15 * risk_assessment$age_risk_score +
    0.20 * risk_assessment$comorbidity_risk_score +
    0.25 * risk_assessment$drug_interaction_risk_score +
    0.10 * risk_assessment$genetic_risk_score +
    0.15 * risk_assessment$laboratory_risk_score +
    0.10 * risk_assessment$ml_bleeding_risk +
    0.05 * risk_assessment$ml_major_bleeding_risk
  )
  
  # Categorize risk levels with confidence assessment
  risk_assessment$risk_category <- cut(
    risk_assessment$composite_risk_score,
    breaks = c(0, 2, 4, 6, Inf),
    labels = c("Low", "Moderate", "High", "Very High"),
    include.lowest = TRUE
  )
  
  # Assess confidence based on data completeness
  data_completeness <- rowMeans(!is.na(patient_data[c("age", "platelet_count", 
                                                     "creatinine_clearance")]))
  risk_assessment$confidence_level <- ifelse(data_completeness > 0.8, "High",
                                           ifelse(data_completeness > 0.6, "Moderate", "Low"))
  
  return(risk_assessment)
}

#' Dosing Recommendation Algorithm
#' Evidence-based dosing recommendations
generate_dosing_recommendations <- function(risk_assessment, patient_data) {
  
  cat("Generating dosing recommendations...\n")
  
  dosing_recs <- data.frame(
    patient_id = risk_assessment$patient_id,
    recommended_dose_mg = 420,
    dose_adjustment_reason = "",
    dose_reduction_percentage = 0,
    special_considerations = "",
    monitoring_frequency = "Standard",
    stringsAsFactors = FALSE
  )
  
  for (i in seq_len(nrow(dosing_recs))) {
    
    risk_cat <- as.character(risk_assessment$risk_category[i])
    composite_score <- risk_assessment$composite_risk_score[i]
    
    # Base dosing recommendations by risk category
    if (risk_cat == "Very High") {
      dosing_recs$recommended_dose_mg[i] <- 280
      dosing_recs$dose_reduction_percentage[i] <- 33
      dosing_recs$dose_adjustment_reason[i] <- "Very high bleeding risk"
      dosing_recs$monitoring_frequency[i] <- "Intensive"
      
    } else if (risk_cat == "High") {
      dosing_recs$recommended_dose_mg[i] <- 280
      dosing_recs$dose_reduction_percentage[i] <- 33
      dosing_recs$dose_adjustment_reason[i] <- "High bleeding risk"
      dosing_recs$monitoring_frequency[i] <- "Enhanced"
      
    } else if (risk_cat == "Moderate") {
      dosing_recs$recommended_dose_mg[i] <- 420
      dosing_recs$dose_reduction_percentage[i] <- 0
      dosing_recs$dose_adjustment_reason[i] <- "Standard dose with close monitoring"
      dosing_recs$monitoring_frequency[i] <- "Enhanced"
      
    } else {
      dosing_recs$recommended_dose_mg[i] <- 420
      dosing_recs$dose_reduction_percentage[i] <- 0
      dosing_recs$dose_adjustment_reason[i] <- "Standard dose"
      dosing_recs$monitoring_frequency[i] <- "Standard"
    }
    
    # Special considerations based on specific factors
    considerations <- c()
    
    # Age considerations
    if ("age" %in% names(patient_data) && patient_data$age[i] >= 75) {
      considerations <- c(considerations, "Elderly patient - consider dose reduction")
    }
    
    # Renal impairment
    if ("creatinine_clearance" %in% names(patient_data) && 
        patient_data$creatinine_clearance[i] < 60) {
      considerations <- c(considerations, "Renal impairment - monitor closely")
      if (patient_data$creatinine_clearance[i] < 30) {
        dosing_recs$recommended_dose_mg[i] <- min(dosing_recs$recommended_dose_mg[i], 280)
        considerations <- c(considerations, "Severe renal impairment - dose reduction required")
      }
    }
    
    # Hepatic impairment
    if ("alt" %in% names(patient_data) && patient_data$alt[i] > 100) {
      considerations <- c(considerations, "Hepatic impairment - monitor liver function")
    }
    
    # Drug interactions
    if ("anticoagulants" %in% names(patient_data) && patient_data$anticoagulants[i]) {
      considerations <- c(considerations, "Concurrent anticoagulation - high bleeding risk")
      dosing_recs$recommended_dose_mg[i] <- min(dosing_recs$recommended_dose_mg[i], 280)
    }
    
    # CYP3A4 interactions
    if ("cyp3a4_inhibitors" %in% names(patient_data) && patient_data$cyp3a4_inhibitors[i]) {
      considerations <- c(considerations, "CYP3A4 inhibitor - consider dose reduction")
      dosing_recs$recommended_dose_mg[i] <- min(dosing_recs$recommended_dose_mg[i], 280)
    }
    
    dosing_recs$special_considerations[i] <- paste(considerations, collapse = "; ")
  }
  
  return(dosing_recs)
}

#' Monitoring Schedule Optimization
#' Personalized monitoring schedules based on risk
generate_monitoring_schedule <- function(risk_assessment, dosing_recs) {
  
  cat("Generating personalized monitoring schedules...\n")
  
  monitoring <- data.frame(
    patient_id = risk_assessment$patient_id,
    cbc_frequency_weeks = 4,
    comprehensive_panel_frequency_weeks = 4,
    bleeding_assessment_frequency_weeks = 12,
    special_monitoring = "",
    alert_thresholds = "",
    stringsAsFactors = FALSE
  )
  
  for (i in seq_len(nrow(monitoring))) {
    
    risk_cat <- as.character(risk_assessment$risk_category[i])
    monitoring_freq <- dosing_recs$monitoring_frequency[i]
    
    # Adjust monitoring frequency based on risk
    if (monitoring_freq == "Intensive") {
      monitoring$cbc_frequency_weeks[i] <- 1
      monitoring$comprehensive_panel_frequency_weeks[i] <- 2
      monitoring$bleeding_assessment_frequency_weeks[i] <- 4
      
    } else if (monitoring_freq == "Enhanced") {
      monitoring$cbc_frequency_weeks[i] <- 2
      monitoring$comprehensive_panel_frequency_weeks[i] <- 4
      monitoring$bleeding_assessment_frequency_weeks[i] <- 8
      
    } else {
      monitoring$cbc_frequency_weeks[i] <- 4
      monitoring$comprehensive_panel_frequency_weeks[i] <- 8
      monitoring$bleeding_assessment_frequency_weeks[i] <- 12
    }
    
    # Special monitoring requirements
    special_monitoring <- c()
    
    if (risk_assessment$laboratory_risk_score[i] >= 2) {
      special_monitoring <- c(special_monitoring, "Enhanced laboratory monitoring")
    }
    
    if (risk_assessment$drug_interaction_risk_score[i] >= 4) {
      special_monitoring <- c(special_monitoring, "Drug interaction monitoring")
    }
    
    monitoring$special_monitoring[i] <- paste(special_monitoring, collapse = "; ")
    
    # Alert thresholds
    thresholds <- c(
      "Platelet count < 75,000",
      "Hemoglobin drop > 2 g/dL",
      "Any bleeding event",
      "ALT > 3x ULN"
    )
    
    monitoring$alert_thresholds[i] <- paste(thresholds, collapse = "; ")
  }
  
  return(monitoring)
}

cat("Clinical Decision Support System loaded successfully!\n")
cat("Functions: advanced_risk_stratification, generate_dosing_recommendations, generate_monitoring_schedule\n")
