# =============================================================================
# ENHANCED MACHINE LEARNING FOR IBRUTINIB QSP MODEL WITH VIRTUAL TRIAL INTEGRATION
# =============================================================================
# This script implements comprehensive machine learning models to predict safety outcomes
# for ibrutinib treatment based on patient profiles with full virtual trial integration
#
# Enhanced Features:
# - Bleeding event predictions with time-to-event modeling
# - Platelet count trajectory prediction
# - Patient outcome stratification and risk scoring
# - Clinical decision support recommendations
# - Integration with virtual clinical trial results (5,000 patients)
# - Advanced explainability with SHAP and LIME analysis
# - Comprehensive performance validation against clinical benchmarks

# Load file utilities first
if (file.exists("ml_file_utils.r")) {
  source("ml_file_utils.r")
} else {
  cat("Warning: ml_file_utils.r not found. Using basic file operations.\n")
}

# Load required libraries with error handling
required_packages <- c("randomForest", "xgboost", "nnet", "pROC", "caret",
                      "dplyr", "ggplot2", "survival", "survminer", "glmnet")

# Load standardized performance utilities
source("ml_performance_utils.r")
optional_packages <- c("corrplot", "ROSE", "DALEX", "iml", "lime")

# Load required packages
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    stop(paste("Required package", pkg, "is not installed. Please install it using: install.packages('", pkg, "')"))
  }
}

# Load optional packages with fallbacks
use_corrplot <- require(corrplot, quietly = TRUE)
use_rose <- require(ROSE, quietly = TRUE)

if (!use_corrplot) {
  cat("corrplot package not available - correlation plots will be skipped\n")
}
if (!use_rose) {
  cat("ROSE package not available - using alternative methods for imbalanced data\n")
}

set.seed(456)

# Source required scripts
# Source required scripts with error handling
if (file.exists("virtual_clinical_trial.r")) {
  source("virtual_clinical_trial.r")
} else {
  stop("virtual_clinical_trial.r not found. Please ensure the file exists in the current directory.")
}

# =============================================================================
# PART 1: DATA PREPROCESSING FOR ML
# =============================================================================

preprocess_trial_data <- function(trial_data) {
  
  cat("Preprocessing data for machine learning...\n")
  
  # Create feature matrix - using actual column names from trial data
  # Use dplyr::select to avoid namespace conflicts
  # Only select columns that actually exist in the trial data
  available_cols <- names(trial_data)
  
  # Define desired columns with fallback mappings
  desired_cols <- c(
    # Demographics
    "age", "sex", "weight", "height", "bmi", "ethnicity", "bsa",
    "creatinine_clearance",
    # Clinical variables (safety-relevant only)
    "platelet_count",
    "hypertension", "diabetes", "cardiovascular_disease", "atrial_fibrillation",
    "alt", "ast", "bilirubin",
    "anticoagulants", "antiplatelets", "cyp3a4_inhibitors", "cyp3a4_inducers", "ppi_use",
    "baseline_aggregation",
    # Genetic factors
    "cyp3a4_activity", "cyp3a5_activity", "metabolizer_phenotype",
    "plcg2_variants",
    # Treatment
    "treatment_arm",
    # PK data (for treated patients)
    "cmax_nm", "auc_nm_h", "css_avg_nm"
  )
  
  # Column name mappings for compatibility
  column_mappings <- list(
    "bleeding_event" = c("bleeding_event_occurred", "bleeding_event"),
    "major_bleeding" = c("major_bleeding_events", "major_bleeding"),
    "bleeding_risk_score" = c("annual_bleeding_risk", "bleeding_risk_score")
  )
  
  # Only select columns that actually exist
  cols_to_select <- intersect(desired_cols, available_cols)
  
  features <- trial_data %>%
    dplyr::select(all_of(cols_to_select))
  
  # Encode categorical variables
  features$sex_male <- as.numeric(features$sex == "Male")
  features$ethnicity_caucasian <- as.numeric(features$ethnicity == "Caucasian")
  features$ethnicity_asian <- as.numeric(features$ethnicity == "Asian")
  features$ethnicity_african_american <- as.numeric(features$ethnicity == "African American")
  
  # CLL stage encoding (only if column exists)
  if ("cll_stage" %in% names(features)) {
    features$cll_stage_numeric <- as.numeric(factor(features$cll_stage, levels = c("0", "I", "II", "III", "IV")))
  }
  
  # Metabolizer phenotype encoding
  features$metabolizer_poor <- as.numeric(features$metabolizer_phenotype == "Poor")
  features$metabolizer_intermediate <- as.numeric(features$metabolizer_phenotype == "Intermediate")
  features$metabolizer_ultrarapid <- as.numeric(features$metabolizer_phenotype == "Ultrarapid")
  
  # Treatment arm encoding
  features$treatment_ibr_420 <- as.numeric(features$treatment_arm == "Ibrutinib_420mg")
  
  # Convert logical to numeric
  logical_cols <- sapply(features, is.logical)
  features[logical_cols] <- lapply(features[logical_cols], as.numeric)
  
  # Remove original categorical columns (only if they exist)
  cols_to_remove <- intersect(c("sex", "ethnicity", "cll_stage", "metabolizer_phenotype", "treatment_arm"), names(features))
  if (length(cols_to_remove) > 0) {
    features <- features %>%
      dplyr::select(-all_of(cols_to_remove))
  }
  
  # Handle missing values (fill with median/mode)
  for (col in names(features)) {
    if (any(is.na(features[[col]]))) {
      if (is.numeric(features[[col]])) {
        features[[col]][is.na(features[[col]])] <- median(features[[col]], na.rm = TRUE)
      } else {
        features[[col]][is.na(features[[col]])] <- 0
      }
    }
  }
  
  # Create target variables from trial data (safety-focused only)
  # Define desired target columns with fallback mappings
  desired_targets <- c(
    "bleeding_event", "major_bleeding", "bleeding_risk_score",
    "collagen_inhibition", "adp_inhibition", "arachidonic_inhibition", "ristocetin_inhibition"
  )
  
  # Apply column mappings to find available targets
  available_targets <- character()
  targets <- data.frame(row.names = 1:nrow(trial_data))
  
  for (target in desired_targets) {
    if (target %in% column_mappings) {
      # Check mapped column names
      mapped_cols <- column_mappings[[target]]
      found_col <- intersect(mapped_cols, names(trial_data))
      if (length(found_col) > 0) {
        targets[[target]] <- trial_data[[found_col[1]]]
        available_targets <- c(available_targets, target)
      }
    } else if (target %in% names(trial_data)) {
      # Direct column match
      targets[[target]] <- trial_data[[target]]
      available_targets <- c(available_targets, target)
    }
  }
  
  # Convert to data frame if not empty
  if (length(available_targets) == 0) {
    targets <- data.frame(dummy_target = rep(0, nrow(trial_data)))
    cat("Warning: No target variables found. Creating dummy target.\n")
  }
  
  # Create derived variables only if base columns exist
  if ("major_bleeding" %in% names(targets)) {
    targets$major_bleeding_event <- as.numeric(targets$major_bleeding > 0)
  } else {
    # Try to find major bleeding events from trial_data directly
    major_bleeding_cols <- c("major_bleeding_events", "major_bleeding")
    found_major <- intersect(major_bleeding_cols, names(trial_data))
    if (length(found_major) > 0) {
      targets$major_bleeding_event <- as.numeric(trial_data[[found_major[1]]] > 0)
    } else {
      targets$major_bleeding_event <- rep(0, nrow(trial_data))  # Default if not available
    }
  }
  
  if ("bleeding_risk_score" %in% names(targets)) {
    # Adjust threshold based on data range
    risk_threshold <- if (max(targets$bleeding_risk_score, na.rm = TRUE) <= 1) 0.15 else 0.5
    targets$high_bleeding_risk <- as.numeric(targets$bleeding_risk_score > risk_threshold)
  }
  
  if ("collagen_inhibition" %in% names(targets)) {
    targets$strong_collagen_inhibition <- as.numeric(targets$collagen_inhibition > 70)
  }
  
  if (all(c("collagen_inhibition", "arachidonic_inhibition") %in% names(targets))) {
    targets$any_major_inhibition <- as.numeric(pmax(targets$collagen_inhibition, targets$arachidonic_inhibition, na.rm = TRUE) > 50)
  }
  
  # Handle missing values in targets
  for (col in names(targets)) {
    if (any(is.na(targets[[col]]))) {
      if (is.numeric(targets[[col]])) {
        # For numeric targets, replace NA with median or 0
        if (col %in% c("bleeding_event", "major_bleeding_event",
                       "high_bleeding_risk", "strong_collagen_inhibition", "any_major_inhibition")) {
          # Binary targets: replace NA with 0 (no event)
          targets[[col]][is.na(targets[[col]])] <- 0
        } else {
          # Continuous targets: replace NA with median
          targets[[col]][is.na(targets[[col]])] <- median(targets[[col]], na.rm = TRUE)
        }
      }
    }
  }
  
  # Combine features and targets
  ml_data <- cbind(features, targets)
  
  cat("Preprocessing complete. Features:", ncol(features), ", Samples:", nrow(ml_data), "\n")
  
  return(list(features = features, targets = targets, ml_data = ml_data))
}

# =============================================================================
# ENHANCED PREDICTION FUNCTIONS
# =============================================================================

#' Time-to-Event Prediction for Bleeding Events
#' Predicts time until first bleeding event using survival analysis
predict_time_to_bleeding <- function(trial_data, features) {

  cat("Training time-to-event model for bleeding events...\n")

  # Prepare survival data
  time_col <- "time_to_first_bleeding"
  event_col <- "bleeding_event_occurred"

  if (!all(c(time_col, event_col) %in% names(trial_data))) {
    cat("Warning: Time-to-event data not available\n")
    return(NULL)
  }

  # Create survival object
  surv_data <- data.frame(
    time = trial_data[[time_col]],
    event = as.numeric(trial_data[[event_col]]),
    features
  )

  # Remove missing values
  complete_cases <- complete.cases(surv_data)
  surv_data <- surv_data[complete_cases, ]

  if (nrow(surv_data) < 50) {
    cat("Warning: Insufficient data for survival analysis\n")
    return(NULL)
  }

  # Fit Cox proportional hazards model
  tryCatch({
    cox_formula <- as.formula(paste("Surv(time, event) ~",
                                   paste(names(features), collapse = " + ")))
    cox_model <- coxph(cox_formula, data = surv_data)

    return(list(
      model = cox_model,
      data = surv_data,
      summary = summary(cox_model)
    ))
  }, error = function(e) {
    cat("Error in Cox model fitting:", e$message, "\n")
    return(NULL)
  })
}

#' Platelet Count Trajectory Prediction
#' Predicts platelet count changes over time
predict_platelet_trajectory <- function(trial_data, features) {

  cat("Training platelet trajectory prediction model...\n")

  # Check for platelet count data
  if (!"platelet_count" %in% names(trial_data)) {
    cat("Warning: Platelet count data not available\n")
    return(NULL)
  }

  # Prepare data for trajectory modeling
  trajectory_data <- data.frame(
    platelet_count = trial_data$platelet_count,
    features
  )

  # Remove missing values
  complete_cases <- complete.cases(trajectory_data)
  trajectory_data <- trajectory_data[complete_cases, ]

  if (nrow(trajectory_data) < 50) {
    cat("Warning: Insufficient data for trajectory modeling\n")
    return(NULL)
  }

  # Train random forest for platelet count prediction
  tryCatch({
    rf_trajectory <- randomForest(
      platelet_count ~ .,
      data = trajectory_data,
      ntree = 500,
      importance = TRUE
    )

    # Calculate performance metrics
    predictions <- predict(rf_trajectory, trajectory_data)
    rmse <- sqrt(mean((trajectory_data$platelet_count - predictions)^2))
    mae <- mean(abs(trajectory_data$platelet_count - predictions))
    r2 <- cor(trajectory_data$platelet_count, predictions)^2

    return(list(
      model = rf_trajectory,
      performance = list(rmse = rmse, mae = mae, r2 = r2),
      predictions = predictions
    ))
  }, error = function(e) {
    cat("Error in trajectory modeling:", e$message, "\n")
    return(NULL)
  })
}

#' Patient Risk Stratification
#' Stratifies patients into risk categories based on multiple factors
stratify_patient_risk <- function(trial_data, ml_predictions) {

  cat("Performing patient risk stratification...\n")

  # Initialize risk scores
  risk_scores <- data.frame(
    patient_id = trial_data$patient_id,
    bleeding_risk = 0,
    major_bleeding_risk = 0,
    overall_risk = 0,
    risk_category = "Low"
  )

  # Add bleeding risk predictions if available
  if (!is.null(ml_predictions$bleeding_risk)) {
    risk_scores$bleeding_risk <- ml_predictions$bleeding_risk
  }

  if (!is.null(ml_predictions$major_bleeding)) {
    risk_scores$major_bleeding_risk <- ml_predictions$major_bleeding
  }

  # Calculate composite risk score
  risk_scores$overall_risk <- (
    0.4 * risk_scores$bleeding_risk +
    0.6 * risk_scores$major_bleeding_risk
  )

  # Categorize risk levels
  risk_scores$risk_category <- cut(
    risk_scores$overall_risk,
    breaks = c(0, 0.3, 0.6, 1.0),
    labels = c("Low", "Moderate", "High"),
    include.lowest = TRUE
  )

  # Add clinical factors for enhanced stratification
  if ("age" %in% names(trial_data)) {
    risk_scores$age_risk <- ifelse(trial_data$age >= 75, "High",
                                  ifelse(trial_data$age >= 65, "Moderate", "Low"))
  }

  if ("anticoagulants" %in% names(trial_data)) {
    risk_scores$anticoag_risk <- ifelse(trial_data$anticoagulants, "High", "Low")
  }

  return(risk_scores)
}

#' Clinical Decision Support System
#' Provides comprehensive clinical recommendations based on ML predictions
generate_clinical_recommendations <- function(patient_data, risk_scores, ml_predictions) {

  cat("Generating clinical decision support recommendations...\n")

  recommendations <- data.frame(
    patient_id = patient_data$patient_id,
    risk_category = risk_scores$risk_category,
    dosing_recommendation = "",
    monitoring_schedule = "",
    discontinuation_alert = FALSE,
    clinical_notes = "",
    stringsAsFactors = FALSE
  )

  for (i in 1:nrow(recommendations)) {

    risk_cat <- as.character(risk_scores$risk_category[i])
    bleeding_risk <- risk_scores$bleeding_risk[i]

    # Dosing recommendations
    if (risk_cat == "High") {
      recommendations$dosing_recommendation[i] <- "Consider dose reduction to 280mg daily"
      recommendations$monitoring_schedule[i] <- "Weekly CBC, monthly comprehensive panel"
      recommendations$discontinuation_alert[i] <- bleeding_risk > 0.8
    } else if (risk_cat == "Moderate") {
      recommendations$dosing_recommendation[i] <- "Standard 420mg daily with close monitoring"
      recommendations$monitoring_schedule[i] <- "Bi-weekly CBC, monthly comprehensive panel"
      recommendations$discontinuation_alert[i] <- bleeding_risk > 0.9
    } else {
      recommendations$dosing_recommendation[i] <- "Standard 420mg daily"
      recommendations$monitoring_schedule[i] <- "Monthly CBC and comprehensive panel"
      recommendations$discontinuation_alert[i] <- FALSE
    }

    # Clinical notes based on specific risk factors
    notes <- c()

    if ("age" %in% names(patient_data) && patient_data$age[i] >= 75) {
      notes <- c(notes, "Elderly patient - increased bleeding risk")
    }

    if ("anticoagulants" %in% names(patient_data) && patient_data$anticoagulants[i]) {
      notes <- c(notes, "Concurrent anticoagulation - high bleeding risk")
    }

    if ("platelet_count" %in% names(patient_data) && patient_data$platelet_count[i] < 100) {
      notes <- c(notes, "Thrombocytopenia - monitor platelet count closely")
    }

    if ("creatinine_clearance" %in% names(patient_data) && patient_data$creatinine_clearance[i] < 60) {
      notes <- c(notes, "Renal impairment - consider dose adjustment")
    }

    recommendations$clinical_notes[i] <- paste(notes, collapse = "; ")
  }

  return(recommendations)
}

#' Enhanced Feature Engineering for Virtual Trial Integration
#' Creates additional features from virtual trial results
engineer_virtual_trial_features <- function(trial_data) {

  cat("Engineering features from virtual trial data...\n")

  enhanced_features <- trial_data

  # Time-based features
  if ("followup_days" %in% names(trial_data)) {
    enhanced_features$followup_months <- trial_data$followup_days / 30.44
    enhanced_features$long_term_followup <- as.numeric(trial_data$followup_days > 365)
  }

  # Bleeding event features
  if (all(c("bleeding_events", "major_bleeding_events") %in% names(trial_data))) {
    enhanced_features$bleeding_event_rate <- trial_data$bleeding_events /
      pmax(trial_data$followup_days / 365, 0.1)
    enhanced_features$major_bleeding_proportion <- trial_data$major_bleeding_events /
      pmax(trial_data$bleeding_events, 1)
  }

  # Pharmacokinetic features
  if (all(c("cmax_nm", "auc_nm_h") %in% names(trial_data))) {
    enhanced_features$pk_exposure_high <- as.numeric(trial_data$cmax_nm >
      quantile(trial_data$cmax_nm, 0.75, na.rm = TRUE))
    enhanced_features$pk_auc_high <- as.numeric(trial_data$auc_nm_h >
      quantile(trial_data$auc_nm_h, 0.75, na.rm = TRUE))
  }

  # Comorbidity score
  comorbidity_cols <- c("hypertension", "diabetes", "cardiovascular_disease",
                       "atrial_fibrillation")
  available_comorbidity <- intersect(comorbidity_cols, names(trial_data))
  if (length(available_comorbidity) > 0) {
    enhanced_features$comorbidity_score <- rowSums(
      trial_data[available_comorbidity], na.rm = TRUE
    )
  }

  # Drug interaction risk
  interaction_cols <- c("anticoagulants", "antiplatelets", "cyp3a4_inhibitors")
  available_interactions <- intersect(interaction_cols, names(trial_data))
  if (length(available_interactions) > 0) {
    enhanced_features$drug_interaction_risk <- rowSums(
      trial_data[available_interactions], na.rm = TRUE
    )
  }

  return(enhanced_features)
}

# =============================================================================
# PART 2: RANDOM FOREST MODELS
# =============================================================================

train_random_forest_models <- function(ml_data) {
  
  cat("Training Random Forest models...\n")
  
  features <- ml_data$features
  targets <- ml_data$targets
  
  # Validate input data
  if (nrow(features) < 50) {
    cat("Warning: Insufficient data for Random Forest training (< 50 samples)\n")
    return(list(models = list(), predictions = list(), performance = list(), 
                test_indices = integer(0), error = "Insufficient data"))
  }
  
  # Split data with stratification for binary targets
  set.seed(123)
  train_idx <- sample(nrow(features), 0.8 * nrow(features))
  
  X_train <- features[train_idx, ]
  X_test <- features[-train_idx, ]
  
  models <- list()
  predictions <- list()
  performance <- list()
  
  # Model 1: Bleeding Risk Prediction
  cat("  - Training bleeding risk model...\n")
  y_train <- targets$bleeding_event[train_idx]
  y_test <- targets$bleeding_event[-train_idx]
  
  # Enhanced validation for binary classification
  y_train_clean <- y_train[!is.na(y_train)]
  if (length(y_train_clean) < 20) {
    cat("    Warning: Insufficient non-missing data for bleeding risk model\n")
  } else if (length(unique(y_train_clean)) < 2) {
    cat("    Warning: Single class in training data for bleeding risk model\n")
  } else if (min(table(y_train_clean)) < 5) {
    cat("    Warning: Very few samples in minority class for bleeding risk model\n")
  } else {
    rf_bleeding <- randomForest(x = X_train, y = as.factor(y_train),
                               ntree = 500, mtry = sqrt(ncol(X_train)),
                               importance = TRUE, class.weight = "balanced")
    
    pred_bleeding <- predict(rf_bleeding, X_test, type = "prob")[, 2]
    
    models$bleeding_risk <- rf_bleeding
    predictions$bleeding_risk <- pred_bleeding
    
    # Calculate standardized performance metrics
    performance$bleeding_risk <- calculate_standardized_performance(
      y_true = y_test,
      y_pred = pred_bleeding,
      threshold = 0.5,
      task_type = "classification"
    )
  }
  
  # Model 3: Major Bleeding Prediction
  cat("  - Training major bleeding model...\n")
  y_train <- targets$major_bleeding_event[train_idx]
  y_test <- targets$major_bleeding_event[-train_idx]
  
  # Enhanced validation for major bleeding model
  y_train_clean <- y_train[!is.na(y_train)]
  
  if (length(y_train_clean) < 20) {
    cat("    Warning: Insufficient non-missing data for major bleeding model\n")
  } else if (length(unique(y_train_clean)) < 2) {
    cat("    Warning: Single class in training data for major bleeding model\n")
  } else if (sum(y_train_clean, na.rm = TRUE) < 5) {
    cat("    Warning: Very few positive cases for major bleeding model\n")
  } else {
    rf_major_bleeding <- randomForest(x = X_train, y = as.factor(y_train),
                                     ntree = 500, mtry = sqrt(ncol(X_train)),
                                     importance = TRUE, class.weight = "balanced")
    
    pred_major_bleeding <- predict(rf_major_bleeding, X_test, type = "prob")[, 2]
    
    models$major_bleeding <- rf_major_bleeding
    predictions$major_bleeding <- pred_major_bleeding
    
    # Calculate standardized performance metrics
    performance$major_bleeding <- calculate_standardized_performance(
      y_true = y_test,
      y_pred = pred_major_bleeding,
      threshold = 0.5,
      task_type = "classification"
    )
  }
  
  # Model 4: Collagen Inhibition Prediction (Regression)
  cat("  - Training collagen inhibition model...\n")
  y_train <- targets$collagen_inhibition[train_idx]
  y_test <- targets$collagen_inhibition[-train_idx]
  
  # Enhanced validation for regression model
  valid_idx <- !is.na(y_train) & is.finite(y_train)
  if (sum(valid_idx) < 20) {
    cat("    Warning: Insufficient valid data for collagen inhibition model\n")
  } else if (var(y_train[valid_idx], na.rm = TRUE) == 0) {
    cat("    Warning: No variance in target variable for collagen inhibition model\n")
  } else {
    rf_collagen <- randomForest(x = X_train[valid_idx, ], y = y_train[valid_idx],
                               ntree = 500, mtry = sqrt(ncol(X_train)),
                               importance = TRUE)
    
    pred_collagen <- predict(rf_collagen, X_test)
    
    models$collagen_inhibition <- rf_collagen
    predictions$collagen_inhibition <- pred_collagen
    
    # Calculate standardized regression performance metrics
    performance$collagen_inhibition <- calculate_standardized_performance(
      y_true = y_test,
      y_pred = pred_collagen,
      task_type = "regression"
    )
  }
  
  cat("Random Forest training complete.\n")
  
  return(list(
    models = models,
    predictions = predictions,
    performance = performance,
    test_indices = -train_idx
  ))
}

# =============================================================================
# PART 3: XGBOOST MODELS
# =============================================================================

train_xgboost_models <- function(ml_data) {
  
  cat("Training XGBoost models...\n")
  
  features <- ml_data$features
  targets <- ml_data$targets
  
  # Validate input data
  if (nrow(features) < 50) {
    cat("Warning: Insufficient data for XGBoost training (< 50 samples)\n")
    return(list(models = list(), predictions = list(), performance = list(), 
                test_indices = integer(0), error = "Insufficient data"))
  }
  
  # Split data
  set.seed(123)
  train_idx <- sample(nrow(features), 0.8 * nrow(features))
  
  X_train <- as.matrix(features[train_idx, ])
  X_test <- as.matrix(features[-train_idx, ])
  
  # Handle infinite and missing values in feature matrices
  X_train[!is.finite(X_train)] <- 0
  X_test[!is.finite(X_test)] <- 0
  
  models <- list()
  predictions <- list()
  performance <- list()
  
  # Model 1: Bleeding Risk
  cat("  - Training bleeding risk XGBoost...\n")
  y_train <- targets$bleeding_event[train_idx]
  y_test <- targets$bleeding_event[-train_idx]
  
  # Enhanced validation for binary classification
  y_train_clean <- y_train[!is.na(y_train)]
  if (length(y_train_clean) < 20) {
    cat("    Warning: Insufficient non-missing data for bleeding risk XGBoost\n")
  } else if (length(unique(y_train_clean)) < 2) {
    cat("    Warning: Single class in training data for bleeding risk XGBoost\n")
  } else if (min(table(y_train_clean)) < 5) {
    cat("    Warning: Very few samples in minority class for bleeding risk XGBoost\n")
  } else {
    dtrain <- xgb.DMatrix(data = X_train, label = y_train)
    dtest <- xgb.DMatrix(data = X_test, label = y_test)
    
    # Calculate scale_pos_weight with safety checks
    pos_count <- sum(y_train == 1, na.rm = TRUE)
    neg_count <- sum(y_train == 0, na.rm = TRUE)
    scale_pos_weight <- ifelse(pos_count > 0, neg_count / pos_count, 1)
    
    params <- list(
      objective = "binary:logistic",
      eval_metric = "auc",
      max_depth = 6,
      eta = 0.1,
      subsample = 0.8,
      colsample_bytree = 0.8,
      scale_pos_weight = scale_pos_weight
    )
    
    # Check if test set has both classes for early stopping
    if (length(unique(y_test)) > 1) {
      xgb_bleeding <- xgb.train(
        params = params,
        data = dtrain,
        nrounds = 100,
        watchlist = list(train = dtrain, test = dtest),
        early_stopping_rounds = 10,
        verbose = 0
      )
    } else {
      # No early stopping if test set has only one class
      xgb_bleeding <- xgb.train(
        params = params,
        data = dtrain,
        nrounds = 100,
        verbose = 0
      )
    }
    
    pred_bleeding <- predict(xgb_bleeding, dtest)
    
    models$bleeding_risk <- xgb_bleeding
    predictions$bleeding_risk <- pred_bleeding
    
    # Calculate standardized performance metrics
    performance$bleeding_risk <- calculate_standardized_performance(
      y_true = y_test,
      y_pred = pred_bleeding,
      threshold = 0.5,
      task_type = "classification"
    )
  }
  
  # Model 3: Collagen Inhibition (Regression)
  cat("  - Training collagen inhibition XGBoost...\n")
  y_train <- targets$collagen_inhibition[train_idx]
  y_test <- targets$collagen_inhibition[-train_idx]
  
  # Enhanced validation for regression model
  valid_idx <- !is.na(y_train) & is.finite(y_train)
  if (sum(valid_idx) < 20) {
    cat("    Warning: Insufficient valid data for collagen inhibition XGBoost\n")
  } else if (var(y_train[valid_idx], na.rm = TRUE) == 0) {
    cat("    Warning: No variance in target variable for collagen inhibition XGBoost\n")
  } else {
    dtrain <- xgb.DMatrix(data = X_train[valid_idx, ], label = y_train[valid_idx])
    
    valid_test_idx <- !is.na(y_test)
    if (sum(valid_test_idx) > 0) {
      dtest <- xgb.DMatrix(data = X_test[valid_test_idx, ], label = y_test[valid_test_idx])
      
      params <- list(
        objective = "reg:squarederror",
        eval_metric = "rmse",
        max_depth = 6,
        eta = 0.1,
        subsample = 0.8,
        colsample_bytree = 0.8
      )
      
      xgb_collagen <- xgb.train(
        params = params,
        data = dtrain,
        nrounds = 100,
        watchlist = list(train = dtrain, test = dtest),
        early_stopping_rounds = 10,
        verbose = 0
      )
      
      pred_collagen <- predict(xgb_collagen, dtest)
      
      models$collagen_inhibition <- xgb_collagen
      predictions$collagen_inhibition <- pred_collagen
      
      # Calculate standardized regression performance metrics
      performance$collagen_inhibition <- calculate_standardized_performance(
        y_true = y_test,
        y_pred = pred_collagen,
        task_type = "regression"
      )
    }
  }
  
  cat("XGBoost training complete.\n")
  
  return(list(
    models = models,
    predictions = predictions,
    performance = performance,
    test_indices = -train_idx
  ))
}

# =============================================================================
# PART 4: NEURAL NETWORK MODELS
# =============================================================================

train_neural_network_models <- function(ml_data) {
  
  cat("Training Neural Network models...\n")
  
  features <- ml_data$features
  targets <- ml_data$targets
  
  # Validate input data
  if (nrow(features) < 50) {
    cat("Warning: Insufficient data for Neural Network training (< 50 samples)\n")
    return(list(models = list(), predictions = list(), performance = list(), 
                test_indices = integer(0), error = "Insufficient data"))
  }
  
  # Normalize features for neural networks with robust scaling
  tryCatch({
    features_scaled <- scale(features)
    
    # Handle missing values and infinite values after scaling
    features_scaled[!is.finite(features_scaled)] <- 0
    
    # Check if scaling resulted in constant features
    constant_features <- apply(features_scaled, 2, function(x) var(x, na.rm = TRUE) == 0)
    if (any(constant_features)) {
      cat(sprintf("Warning: %d constant features detected after scaling\n", sum(constant_features)))
    }
  }, error = function(e) {
    cat("Warning: Feature scaling failed, using original features\n")
    features_scaled <- as.matrix(features)
    features_scaled[!is.finite(features_scaled)] <- 0
  })
  
  # Split data
  set.seed(123)
  train_idx <- sample(nrow(features_scaled), 0.8 * nrow(features_scaled))
  
  X_train <- features_scaled[train_idx, ]
  X_test <- features_scaled[-train_idx, ]
  
  models <- list()
  predictions <- list()
  performance <- list()
  
  # Model 1: Bleeding Risk
  cat("  - Training bleeding risk neural network...\n")
  y_train <- targets$bleeding_event[train_idx]
  y_test <- targets$bleeding_event[-train_idx]
  
  # Enhanced validation for binary classification
  y_train_clean <- y_train[!is.na(y_train)]
  if (length(y_train_clean) < 20) {
    cat("    Warning: Insufficient non-missing data for bleeding risk neural network\n")
  } else if (length(unique(y_train_clean)) < 2) {
    cat("    Warning: Single class in training data for bleeding risk neural network\n")
  } else if (min(table(y_train_clean)) < 5) {
    cat("    Warning: Very few samples in minority class for bleeding risk neural network\n")
  } else {
    # Calculate class weights with safety checks
    pos_count <- sum(y_train == 1, na.rm = TRUE)
    neg_count <- sum(y_train == 0, na.rm = TRUE)
    weight_ratio <- ifelse(pos_count > 0, neg_count / pos_count, 1)
    class_weights <- ifelse(y_train == 1, weight_ratio, 1)
    
    nn_bleeding <- nnet(X_train, y_train,
                       size = 10, decay = 0.1, maxit = 200,
                       weights = class_weights, trace = FALSE)
    
    pred_bleeding <- predict(nn_bleeding, X_test)

    # Extract probability for positive class (class 1)
    if (is.matrix(pred_bleeding) && ncol(pred_bleeding) > 1) {
      pred_bleeding <- pred_bleeding[, 2]  # Probability of positive class
    } else if (is.matrix(pred_bleeding)) {
      pred_bleeding <- pred_bleeding[, 1]  # Use first column if only one column
    } else {
      pred_bleeding <- as.numeric(pred_bleeding)  # Convert vector to numeric
    }

    models$bleeding_risk <- nn_bleeding
    predictions$bleeding_risk <- pred_bleeding

    # Calculate standardized performance metrics
    performance$bleeding_risk <- calculate_standardized_performance(
      y_true = y_test,
      y_pred = pred_bleeding,
      threshold = 0.5,
      task_type = "classification"
    )
  }
  
  # Model 2: Collagen Inhibition (Regression)
  cat("  - Training collagen inhibition neural network...\n")
  y_train <- targets$collagen_inhibition[train_idx]
  y_test <- targets$collagen_inhibition[-train_idx]
  
  # Enhanced validation for regression model
  valid_idx <- !is.na(y_train) & is.finite(y_train)
  if (sum(valid_idx) < 20) {
    cat("    Warning: Insufficient valid data for collagen inhibition neural network\n")
  } else if (var(y_train[valid_idx], na.rm = TRUE) == 0) {
    cat("    Warning: No variance in target variable for collagen inhibition neural network\n")
  } else {
    nn_collagen <- nnet(X_train[valid_idx, ], y_train[valid_idx],
                       size = 15, decay = 0.1, maxit = 200,
                       linout = TRUE, trace = FALSE)
    
    pred_collagen <- predict(nn_collagen, X_test)
    
    models$collagen_inhibition <- nn_collagen
    predictions$collagen_inhibition <- pred_collagen
    
    # Calculate standardized regression performance metrics
    performance$collagen_inhibition <- calculate_standardized_performance(
      y_true = y_test,
      y_pred = pred_collagen,
      task_type = "regression"
    )
  }
  
  cat("Neural Network training complete.\n")
  
  return(list(
    models = models,
    predictions = predictions,
    performance = performance,
    test_indices = -train_idx,
    feature_scaling = list(
      center = attr(features_scaled, "scaled:center"),
      scale = attr(features_scaled, "scaled:scale")
    )
  ))
}

# =============================================================================
# PART 5: MODEL COMPARISON AND ENSEMBLE
# =============================================================================

compare_models <- function(rf_results, xgb_results, nn_results) {
  
  cat("Comparing model performance...\n")
  
  # Create comparison dataframe
  comparison <- data.frame(
    Model = character(),
    Outcome = character(),
    Metric = character(),
    Value = numeric(),
    stringsAsFactors = FALSE
  )
  
  # Helper function to add results with safety checks
  add_results <- function(results, model_name) {
    if (!is.null(results$performance) && length(results$performance) > 0) {
      for (outcome in names(results$performance)) {
        if (!is.null(results$performance[[outcome]]) && length(results$performance[[outcome]]) > 0) {
          for (metric in names(results$performance[[outcome]])) {
            metric_value <- results$performance[[outcome]][[metric]]
            # Only add numeric values (skip notes and other non-numeric entries)
            if (is.numeric(metric_value) && !is.na(metric_value)) {
              comparison <<- rbind(comparison, data.frame(
                Model = model_name,
                Outcome = outcome,
                Metric = metric,
                Value = metric_value
              ))
            }
          }
        }
      }
    }
  }
  
  # Add results from all models
  add_results(rf_results, "Random Forest")
  add_results(xgb_results, "XGBoost")
  add_results(nn_results, "Neural Network")
  
  # Print comparison table
  cat("\n=== MODEL PERFORMANCE COMPARISON ===\n")
  print(comparison)
  
  # Find best models for each outcome with safety checks
  if (nrow(comparison) > 0) {
    best_models <- comparison %>%
      filter(Metric %in% c("auc", "r2")) %>%
      group_by(Outcome, Metric) %>%
      slice_max(Value, n = 1) %>%
      ungroup()
  } else {
    best_models <- data.frame(
      Model = character(0),
      Outcome = character(0),
      Metric = character(0),
      Value = numeric(0)
    )
  }
  
  cat("\n=== BEST PERFORMING MODELS ===\n")
  print(best_models)
  
  return(list(
    comparison = comparison,
    best_models = best_models
  ))
}

# =============================================================================
# PART 6: FEATURE IMPORTANCE ANALYSIS
# =============================================================================

analyze_feature_importance <- function(rf_results, xgb_results, feature_names) {
  
  cat("Analyzing feature importance...\n")
  
  importance_data <- list()
  
  # Random Forest importance
  for (outcome in names(rf_results$models)) {
    if (!is.null(rf_results$models[[outcome]])) {
      imp <- importance(rf_results$models[[outcome]])
      if (ncol(imp) >= 2) {
        importance_data[[paste0("RF_", outcome)]] <- data.frame(
          Feature = rownames(imp),
          Importance = imp[, 1],  # Mean Decrease Accuracy
          Model = "Random Forest",
          Outcome = outcome
        )
      }
    }
  }
  
  # XGBoost importance
  for (outcome in names(xgb_results$models)) {
    if (!is.null(xgb_results$models[[outcome]])) {
      imp <- xgb.importance(model = xgb_results$models[[outcome]])
      importance_data[[paste0("XGB_", outcome)]] <- data.frame(
        Feature = imp$Feature,
        Importance = imp$Gain,
        Model = "XGBoost",
        Outcome = outcome
      )
    }
  }
  
  # Combine all importance data with safety checks
  if (length(importance_data) > 0) {
    all_importance <- do.call(rbind, importance_data)
  } else {
    all_importance <- data.frame()
  }
  
  if (nrow(all_importance) > 0) {
    # Plot feature importance
    p1 <- ggplot(all_importance, aes(x = reorder(Feature, Importance), y = Importance, fill = Model)) +
      geom_bar(stat = "identity", position = "dodge") +
      facet_wrap(~Outcome, scales = "free") +
      coord_flip() +
      labs(title = "Feature Importance by Model and Outcome",
           x = "Features", y = "Importance") +
      theme_minimal() +
      theme(axis.text.y = element_text(size = 8))
    
    ggsave("feature_importance.png", p1, width = 15, height = 10)
    
    # Top 10 features for each outcome
    top_features <- all_importance %>%
      group_by(Outcome, Model) %>%
      slice_max(Importance, n = 10) %>%
      ungroup()
    
    cat("\n=== TOP 10 IMPORTANT FEATURES ===\n")
    print(top_features)
    
    return(list(
      all_importance = all_importance,
      top_features = top_features,
      plot = p1
    ))
  } else {
    cat("No feature importance data available - no models were successfully trained.\n")
    return(list(
      all_importance = data.frame(),
      top_features = data.frame(),
      plot = NULL,
      note = "No models trained successfully"
    ))
  }
}

# =============================================================================
# PART 7: ROBUST ERROR HANDLING UTILITIES
# =============================================================================

#' Safe Model Training Wrapper
#' Provides comprehensive error handling for ML model training
safe_model_training <- function(training_func, ml_data, model_name) {
  tryCatch({
    result <- training_func(ml_data)
    
    # Validate result structure
    if (!is.list(result) || is.null(result$models)) {
      warning(sprintf("Invalid result structure from %s training", model_name))
      return(list(models = list(), predictions = list(), performance = list(), 
                  error = "Invalid result structure"))
    }
    
    return(result)
  }, error = function(e) {
    cat(sprintf("Error in %s training: %s\n", model_name, e$message))
    return(list(models = list(), predictions = list(), performance = list(), 
                error = e$message))
  }, warning = function(w) {
    cat(sprintf("Warning in %s training: %s\n", model_name, w$message))
    # Continue with training despite warnings
    result <- training_func(ml_data)
    return(result)
  })
}

#' Validate ML Data Structure
validate_ml_data <- function(ml_data) {
  if (!is.list(ml_data)) {
    stop("ml_data must be a list")
  }
  
  if (!"features" %in% names(ml_data) || !"targets" %in% names(ml_data)) {
    stop("ml_data must contain 'features' and 'targets' elements")
  }
  
  if (nrow(ml_data$features) == 0) {
    stop("No feature data available")
  }
  
  if (ncol(ml_data$features) == 0) {
    stop("No features available")
  }
  
  # Check for minimum data requirements
  if (nrow(ml_data$features) < 20) {
    warning("Very small dataset (< 20 samples) - results may be unreliable")
  }
  
  return(TRUE)
}

# =============================================================================
# PART 8: MAIN ML PIPELINE FUNCTION
# =============================================================================

train_ml_models <- function(trial_data = NULL, save_models = TRUE) {
  
  cat("=== MACHINE LEARNING PIPELINE ===\n")
  
  # Generate trial data if not provided
  if (is.null(trial_data)) {
    cat("Generating virtual trial data...\n")
    # Check if the function exists before calling
    if (exists("run_enhanced_virtual_clinical_trial")) {
      tryCatch({
        trial_data <- run_enhanced_virtual_clinical_trial(n_patients = 5000, save_results = FALSE)
      }, error = function(e) {
        cat("Error generating trial data with save_results=FALSE, trying without...\n")
        trial_data <- run_enhanced_virtual_clinical_trial(n_patients = 5000)
      })
    } else {
      stop("No virtual clinical trial function found. Please ensure virtual_clinical_trial.r is properly loaded.")
    }
  }
  
  # Enhanced feature engineering from virtual trial data
  cat("\nEnhancing features from virtual trial data...\n")
  enhanced_trial_data <- engineer_virtual_trial_features(trial_data)

  # Preprocess data with error handling
  cat("\nPreprocessing trial data...\n")
  tryCatch({
    ml_data <- preprocess_trial_data(enhanced_trial_data)
    validate_ml_data(ml_data)
  }, error = function(e) {
    stop(sprintf("Data preprocessing failed: %s", e$message))
  })

  cat(sprintf("Data preprocessing complete: %d samples, %d features\n",
              nrow(ml_data$features), ncol(ml_data$features)))
  
  # Train all models with robust error handling
  cat("\nTraining machine learning models...\n")
  
  rf_results <- safe_model_training(train_random_forest_models, ml_data, "Random Forest")
  xgb_results <- safe_model_training(train_xgboost_models, ml_data, "XGBoost")
  nn_results <- safe_model_training(train_neural_network_models, ml_data, "Neural Network")
  
  # Compare models
  comparison <- compare_models(rf_results, xgb_results, nn_results)
  
  # Analyze feature importance
  importance_analysis <- analyze_feature_importance(rf_results, xgb_results, names(ml_data$features))

  # Enhanced predictions and analysis
  cat("\nGenerating enhanced predictions and clinical insights...\n")

  # Time-to-event prediction
  tte_model <- predict_time_to_bleeding(enhanced_trial_data, ml_data$features)

  # Platelet trajectory prediction
  trajectory_model <- predict_platelet_trajectory(enhanced_trial_data, ml_data$features)

  # Patient risk stratification
  # Combine predictions from best performing models
  combined_predictions <- list()
  if (!is.null(rf_results$predictions$bleeding_risk)) {
    combined_predictions$bleeding_risk <- rf_results$predictions$bleeding_risk
  }
  if (!is.null(rf_results$predictions$major_bleeding)) {
    combined_predictions$major_bleeding <- rf_results$predictions$major_bleeding
  }

  risk_stratification <- stratify_patient_risk(enhanced_trial_data, combined_predictions)

  # Clinical decision support recommendations
  clinical_recommendations <- generate_clinical_recommendations(
    enhanced_trial_data, risk_stratification, combined_predictions
  )

  # Save models if requested
  if (save_models) {
    # Save results using safe file utilities
    results_dir <- get_ml_results_dir()
    results_file <- build_path(results_dir, "ml_models_results.rds")
    
    safe_file_save(list(
      rf_results = rf_results,
      xgb_results = xgb_results,
      nn_results = nn_results,
      comparison = comparison,
      importance_analysis = importance_analysis,
      ml_data = ml_data,
      tte_model = tte_model,
      trajectory_model = trajectory_model,
      risk_stratification = risk_stratification,
      clinical_recommendations = clinical_recommendations
    ), results_file)

    # Save clinical recommendations separately
    recommendations_file <- build_path(results_dir, "clinical_recommendations.csv")
    safe_csv_save(clinical_recommendations, recommendations_file)

    # Save risk stratification
    risk_file <- build_path(results_dir, "patient_risk_stratification.csv")
    safe_csv_save(risk_stratification, risk_file)

    cat(sprintf("\nModels saved to '%s'\n", results_file))
    cat(sprintf("Clinical recommendations saved to '%s'\n", recommendations_file))
    cat(sprintf("Risk stratification saved to '%s'\n", risk_file))
  }

  return(list(
    rf_results = rf_results,
    xgb_results = xgb_results,
    nn_results = nn_results,
    comparison = comparison,
    importance_analysis = importance_analysis,
    ml_data = ml_data,
    tte_model = tte_model,
    trajectory_model = trajectory_model,
    risk_stratification = risk_stratification,
    clinical_recommendations = clinical_recommendations,
    enhanced_trial_data = enhanced_trial_data
  ))
}

cat("Enhanced machine learning module with virtual trial integration loaded!\n")
cat("Features: Bleeding prediction, time-to-event modeling, clinical decision support\n")
cat("Use train_ml_models() to train comprehensive safety prediction models.\n")