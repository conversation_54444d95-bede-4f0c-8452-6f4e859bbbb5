# =============================================================================
# CALIBRATED MODEL VISUALIZATIONS
# =============================================================================
# Generate comprehensive visualizations for calibrated model validation

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(gridExtra)
  library(scales)
  library(reshape2)
})

cat("=== GENERATING CALIBRATED MODEL VISUALIZATIONS ===\n\n")

# Load file utilities and data
source("ml_file_utils.r")
results_dir <- get_ml_results_dir()

# Load comparison results
comparison_file <- build_path(results_dir, "calibration_validation_results.csv")
comparison_data <- read.csv(comparison_file, stringsAsFactors = FALSE)

# Load age-stratified data
age_file <- build_path(results_dir, "age_stratified_calibration_comparison.csv")
age_data <- read.csv(age_file, stringsAsFactors = FALSE)

# Load literature data for extended comparison
literature_file <- build_path(results_dir, "literature_validation_comparison.csv")
literature_data <- read.csv(literature_file, stringsAsFactors = FALSE)

cat("Loaded visualization data\n\n")

# 1. Model Comparison Bar Chart
cat("1. Creating model comparison visualization...\n")

# Prepare data for plotting
plot_data <- comparison_data %>%
  filter(Model != "Literature Mean") %>%
  select(Model, Any_Bleeding_Rate, Major_Bleeding_Rate, Fatal_Bleeding_Rate, ICH_Rate, GI_Bleeding_Rate) %>%
  melt(id.vars = "Model", variable.name = "Outcome", value.name = "Rate") %>%
  mutate(
    Outcome_Clean = case_when(
      Outcome == "Any_Bleeding_Rate" ~ "Any Bleeding",
      Outcome == "Major_Bleeding_Rate" ~ "Major Bleeding",
      Outcome == "Fatal_Bleeding_Rate" ~ "Fatal Bleeding",
      Outcome == "ICH_Rate" ~ "ICH",
      Outcome == "GI_Bleeding_Rate" ~ "GI Bleeding"
    )
  )

# Add literature benchmarks
lit_benchmarks <- data.frame(
  Model = "Literature Mean",
  Outcome = "Any_Bleeding_Rate",  # Add missing column
  Outcome_Clean = c("Any Bleeding", "Major Bleeding", "Fatal Bleeding", "ICH", "GI Bleeding"),
  Rate = c(24.0, 6.1, 0.8, 1.1, 3.7),
  stringsAsFactors = FALSE
)

# Fix the Outcome column for literature benchmarks
lit_benchmarks$Outcome <- c("Any_Bleeding_Rate", "Major_Bleeding_Rate", "Fatal_Bleeding_Rate", "ICH_Rate", "GI_Bleeding_Rate")

combined_plot_data <- rbind(plot_data, lit_benchmarks)

p1 <- ggplot(combined_plot_data, aes(x = Outcome_Clean, y = Rate, fill = Model)) +
  geom_col(position = "dodge", alpha = 0.8) +
  labs(
    title = "Calibrated Model Validation: Bleeding Rates Comparison",
    subtitle = "Original vs Calibrated vs Literature Benchmarks",
    x = "Bleeding Outcome",
    y = "Rate (%)",
    fill = "Model"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold", size = 14),
    plot.subtitle = element_text(hjust = 0.5, size = 12),
    axis.text.x = element_text(angle = 45, hjust = 1),
    legend.position = "bottom"
  ) +
  scale_fill_manual(values = c("Original Conservative" = "coral", 
                              "Calibrated Literature-Aligned" = "steelblue", 
                              "Literature Mean" = "darkgreen"))

ggsave(build_path(results_dir, "calibrated_model_comparison.png"), p1, width = 12, height = 8)

# 2. Improvement Metrics Visualization
cat("2. Creating improvement metrics visualization...\n")

improvement_data <- data.frame(
  Outcome = c("Any Bleeding", "Major Bleeding"),
  Original_Fold = c(2.4, 3.9),
  Calibrated_Fold = c(1.5, 1.9),
  Improvement_Factor = c(1.6, 2.0)
)

p2 <- ggplot(improvement_data, aes(x = Outcome)) +
  geom_col(aes(y = Original_Fold, fill = "Original"), alpha = 0.7, position = "dodge") +
  geom_col(aes(y = Calibrated_Fold, fill = "Calibrated"), alpha = 0.7, position = "dodge") +
  geom_hline(yintercept = 1, linetype = "dashed", color = "red", size = 1) +
  labs(
    title = "Model Calibration Improvement",
    subtitle = "Fold differences from literature (lower is better)",
    x = "Bleeding Outcome",
    y = "Fold Difference from Literature",
    fill = "Model"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    legend.position = "bottom"
  ) +
  scale_fill_manual(values = c("Original" = "coral", "Calibrated" = "steelblue")) +
  annotate("text", x = 1, y = 2.6, label = "1.6× improvement", size = 3, color = "blue") +
  annotate("text", x = 2, y = 4.1, label = "2.0× improvement", size = 3, color = "blue")

ggsave(build_path(results_dir, "calibration_improvement_metrics.png"), p2, width = 10, height = 6)

# 3. Literature Comparison with All Studies
cat("3. Creating comprehensive literature comparison...\n")

# Prepare extended literature comparison
lit_studies <- literature_data %>%
  filter(Study != "Virtual Clinical Trial") %>%
  select(Study, Any_Bleeding_Rate, Major_Bleeding_Rate) %>%
  mutate(Study_Type = "Literature")

# Add both model results
model_results <- data.frame(
  Study = c("Original Model", "Calibrated Model"),
  Any_Bleeding_Rate = c(57.6, 37.0),
  Major_Bleeding_Rate = c(23.9, 11.7),
  Study_Type = c("Original", "Calibrated"),
  stringsAsFactors = FALSE
)

extended_comparison <- rbind(lit_studies, model_results)

p3 <- ggplot(extended_comparison, aes(x = reorder(Study, Any_Bleeding_Rate), 
                                     y = Any_Bleeding_Rate, 
                                     fill = Study_Type)) +
  geom_col(alpha = 0.8) +
  geom_hline(yintercept = 24.0, linetype = "dashed", color = "red", size = 1) +
  labs(
    title = "Any Bleeding Rates: Calibrated Model vs Literature",
    subtitle = "Red line = Literature mean (24.0%)",
    x = "Study",
    y = "Any Bleeding Rate (%)",
    fill = "Source"
  ) +
  theme_minimal() +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    legend.position = "bottom"
  ) +
  scale_fill_manual(values = c("Literature" = "lightblue", 
                              "Original" = "coral", 
                              "Calibrated" = "darkblue"))

ggsave(build_path(results_dir, "calibrated_literature_comparison.png"), p3, width = 14, height = 8)

# 4. Age-Stratified Validation
cat("4. Creating age-stratified validation plots...\n")

# Reshape age data for plotting
age_plot_data <- age_data %>%
  select(Model, Age_65_Any, Age_70_Any, Age_75_Any, Age_65_Major, Age_70_Major, Age_75_Major) %>%
  melt(id.vars = "Model", variable.name = "Age_Outcome", value.name = "Rate") %>%
  mutate(
    Age_Group = case_when(
      grepl("65", Age_Outcome) ~ "≥65 years",
      grepl("70", Age_Outcome) ~ "≥70 years",
      grepl("75", Age_Outcome) ~ "≥75 years"
    ),
    Outcome_Type = ifelse(grepl("Any", Age_Outcome), "Any Bleeding", "Major Bleeding")
  )

p4 <- ggplot(age_plot_data, aes(x = Age_Group, y = Rate, fill = Model)) +
  geom_col(position = "dodge", alpha = 0.8) +
  facet_wrap(~Outcome_Type, scales = "free_y") +
  labs(
    title = "Age-Stratified Bleeding Rates: Model Calibration Validation",
    subtitle = "Comparison with SEER-Medicare real-world data",
    x = "Age Group",
    y = "Bleeding Rate (%)",
    fill = "Model"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    strip.text = element_text(face = "bold"),
    legend.position = "bottom"
  ) +
  scale_fill_manual(values = c("Literature" = "darkgreen", 
                              "Original" = "coral", 
                              "Calibrated" = "steelblue"))

ggsave(build_path(results_dir, "calibrated_age_stratified_validation.png"), p4, width = 12, height = 6)

# 5. Risk Ratio Validation
cat("5. Creating risk ratio validation plot...\n")

rr_data <- data.frame(
  Model = c("RESONATE", "Meta-analysis", "ELEVATE-TN", "Original Model", "Calibrated Model"),
  Any_Bleeding_RR = c(7.60, 6.00, 5.42, 7.18, 7.24),
  Major_Bleeding_RR = c(6.20, 3.81, 3.67, 7.91, 5.60),
  Model_Type = c("Literature", "Literature", "Literature", "Original", "Calibrated")
)

p5 <- ggplot(rr_data, aes(x = Any_Bleeding_RR, y = Major_Bleeding_RR, color = Model_Type, size = Model_Type)) +
  geom_point(alpha = 0.8) +
  geom_text(aes(label = Model), hjust = 0.5, vjust = -0.5, size = 3) +
  geom_hline(yintercept = 6.0, linetype = "dashed", color = "red", alpha = 0.5) +
  geom_vline(xintercept = 7.6, linetype = "dashed", color = "red", alpha = 0.5) +
  labs(
    title = "Risk Ratio Validation: Calibrated Model Performance",
    subtitle = "Red lines = Literature targets (Any: 7.6×, Major: 6.0×)",
    x = "Any Bleeding Risk Ratio",
    y = "Major Bleeding Risk Ratio",
    color = "Source",
    size = "Source"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    legend.position = "bottom"
  ) +
  scale_color_manual(values = c("Literature" = "darkgreen", 
                               "Original" = "coral", 
                               "Calibrated" = "darkblue")) +
  scale_size_manual(values = c("Literature" = 3, "Original" = 4, "Calibrated" = 5))

ggsave(build_path(results_dir, "calibrated_risk_ratio_validation.png"), p5, width = 10, height = 8)

# 6. Model Performance Summary Dashboard
cat("6. Creating model performance summary dashboard...\n")

# Create summary metrics
summary_metrics <- data.frame(
  Metric = c("Any Bleeding Alignment", "Major Bleeding Alignment", "Risk Ratio Preservation", "Age Gradient"),
  Original_Score = c(2.4, 3.9, 7.2, 3.0),  # Fold differences and scores
  Calibrated_Score = c(1.5, 1.9, 7.2, 1.3),
  Target_Score = c(1.0, 1.0, 7.6, 1.0),
  stringsAsFactors = FALSE
)

# Calculate performance scores (closer to 1.0 is better for alignment metrics)
summary_metrics$Original_Performance <- pmin(summary_metrics$Target_Score / summary_metrics$Original_Score, 1.0) * 100
summary_metrics$Calibrated_Performance <- pmin(summary_metrics$Target_Score / summary_metrics$Calibrated_Score, 1.0) * 100

performance_data <- summary_metrics %>%
  select(Metric, Original_Performance, Calibrated_Performance) %>%
  melt(id.vars = "Metric", variable.name = "Model", value.name = "Performance") %>%
  mutate(Model = ifelse(Model == "Original_Performance", "Original", "Calibrated"))

p6 <- ggplot(performance_data, aes(x = Metric, y = Performance, fill = Model)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_hline(yintercept = 100, linetype = "dashed", color = "red") +
  labs(
    title = "Model Performance Summary",
    subtitle = "Performance scores (100% = perfect alignment with literature)",
    x = "Performance Metric",
    y = "Performance Score (%)",
    fill = "Model"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5),
    axis.text.x = element_text(angle = 45, hjust = 1),
    legend.position = "bottom"
  ) +
  scale_fill_manual(values = c("Original" = "coral", "Calibrated" = "steelblue")) +
  ylim(0, 110)

ggsave(build_path(results_dir, "calibrated_performance_summary.png"), p6, width = 12, height = 6)

cat("\n=== CALIBRATED VISUALIZATIONS COMPLETED ===\n")
cat("Generated files:\n")
cat("- calibrated_model_comparison.png\n")
cat("- calibration_improvement_metrics.png\n")
cat("- calibrated_literature_comparison.png\n")
cat("- calibrated_age_stratified_validation.png\n")
cat("- calibrated_risk_ratio_validation.png\n")
cat("- calibrated_performance_summary.png\n\n")

cat("✓ Comprehensive calibrated model visualizations complete\n")
cat("✓ Literature validation plots updated\n")
cat("✓ Performance improvement documented\n")
cat("✓ Age-stratified validation visualized\n")
